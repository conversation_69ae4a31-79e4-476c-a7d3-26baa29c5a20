/* 话题热榜页面样式 */
page {
  background-color: #f5f5f5;
}

.container {
  min-height: 100vh;
  padding: 20rpx;
}

/* 页面头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.more-link {
  font-size: 28rpx;
  color: #999;
}

/* 话题列表样式 */
.topic-list {
  margin-top: 20rpx;
}

.topic-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 话题图标样式 */
.topic-icon {
  margin-right: 24rpx;
}

.icon-circle {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff9a56, #ffcc70);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
}

/* 话题内容样式 */
.topic-content {
  flex: 1;
  margin-right: 20rpx;
}

.topic-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.topic-stats {
  display: flex;
  align-items: center;
}

.participant-count {
  font-size: 24rpx;
  color: #999;
}

/* 加入讨论按钮样式 */
.join-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #ff9a56, #ffcc70);
  color: #fff;
  font-size: 26rpx;
  border-radius: 30rpx;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .topic-item {
    padding: 24rpx;
    margin: 0 20rpx 16rpx;
  }
  
  .title {
    font-size: 32rpx;
  }
  
  .topic-title {
    font-size: 28rpx;
  }
  
  .join-btn {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
  }
}

@media screen and (min-width: 768px) {
  .container {
    max-width: 750rpx;
    margin: 0 auto;
  }
  
  .topic-item {
    padding: 40rpx;
  }
  
  .title {
    font-size: 40rpx;
  }
} 
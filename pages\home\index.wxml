<!--pages/home/<USER>
<!--首页占位-->
<view class="container">
  <!-- 顶部轮播图 -->
  <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
    <swiper-item wx:for="{{bannerList}}" wx:key="id">
      <image class="banner-image" src="{{item.image}}" mode="aspectFill" bindtap="onBannerTap" data-id="{{item.id}}"></image>
      <view class="banner-text">{{item.title}}</view>
    </swiper-item>
  </swiper>

  <!-- 快捷功能区 -->
  <view class="quick-menu">
    <view class="quick-item" wx:for="{{quickMenus}}" wx:key="id" bindtap="onQuickMenuTap" data-type="{{item.type}}">
      <view class="quick-icon {{item.iconClass}}">{{item.icon}}</view>
      <text class="quick-text">{{item.name}}</text>
    </view>
  </view>

  <!-- 热门推荐 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">热门推荐</text>
      <text class="section-more" bindtap="onMoreRecommendTap">查看更多 ></text>
    </view>
    <view class="recommend-list">
      <view class="recommend-item" wx:for="{{recommendList}}" wx:key="id" bindtap="onRecommendTap" data-id="{{item.id}}">
        <image class="recommend-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="recommend-content">
          <text class="recommend-title">{{item.title}}</text>
          <text class="recommend-desc">{{item.description}}</text>
          <view class="recommend-stats">
            <text class="recommend-views">{{item.views}}次浏览</text>
            <text class="recommend-likes">{{item.likes}}人点赞</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 最新动态 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">最新动态</text>
      <text class="section-more" bindtap="onMoreNewsTap">查看更多 ></text>
    </view>
    <view class="news-list">
      <view class="news-item" wx:for="{{newsList}}" wx:key="id" bindtap="onNewsTap" data-id="{{item.id}}">
        <view class="news-content">
          <text class="news-title">{{item.title}}</text>
          <text class="news-time">{{item.time}}</text>
        </view>
        <image class="news-image" src="{{item.image}}" mode="aspectFill"></image>
      </view>
    </view>
  </view>
</view>
{"description": "话题热榜小程序项目配置文件", "packOptions": {"ignore": [{"type": "file", "value": ".eslintrc.js"}]}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "useApiHook": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "bundle": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "touristappid", "projectname": "topic-top", "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "isGameTourist": false, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "plugin": {"current": -1, "list": []}, "gamePlugin": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}}
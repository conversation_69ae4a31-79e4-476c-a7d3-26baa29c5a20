/* 首页样式 */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
}

/* 测试样式 */
.test-section {
  background-color: #fff;
  padding: 40rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  text-align: center;
}

.test-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.test-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

/* 简化轮播图样式 */
.simple-banner {
  background: linear-gradient(135deg, #4CAF50, #81C784);
  padding: 60rpx 40rpx;
  margin: 20rpx 0;
  border-radius: 20rpx;
  text-align: center;
}

.banner-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

/* 轮播图样式 */
.banner-swiper {
  height: 300rpx;
  margin-bottom: 20rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-text {
  position: absolute;
  bottom: 20rpx;
  left: 30rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 快捷菜单样式 */
.quick-menu {
  display: flex;
  background-color: #fff;
  padding: 40rpx 0;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  margin: 0 30rpx 20rpx;
}

.quick-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.quick-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff9a56, #ffcc70);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 16rpx;
}

.quick-text {
  font-size: 24rpx;
  color: #666;
}

/* 区块样式 */
.section {
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #999;
}

/* 推荐列表样式 */
.recommend-list {
  padding: 0 30rpx;
}

.recommend-item {
  display: flex;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.recommend-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.recommend-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.recommend-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.recommend-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  /* 限制显示两行 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recommend-stats {
  display: flex;
  justify-content: space-between;
}

.recommend-views,
.recommend-likes {
  font-size: 22rpx;
  color: #999;
}

/* 新闻列表样式 */
.news-list {
  padding: 0 30rpx;
}

.news-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.news-content {
  flex: 1;
  margin-right: 20rpx;
}

.news-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  /* 限制显示两行 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-time {
  font-size: 22rpx;
  color: #999;
}

.news-image {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .banner-swiper {
    height: 250rpx;
  }
  
  .quick-icon {
    width: 70rpx;
    height: 70rpx;
    font-size: 36rpx;
  }
  
  .recommend-image {
    width: 140rpx;
    height: 100rpx;
  }
  
  .news-image {
    width: 100rpx;
    height: 70rpx;
  }
}

@media screen and (min-width: 768px) {
  .container {
    max-width: 750rpx;
    margin: 0 auto;
  }
} 
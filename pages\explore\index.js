// pages/explore/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    currentCategory: 1, // 当前选中的分类
    
    // 分类数据
    categories: [
      { id: 1, name: '全部' },
      { id: 2, name: '健康' },
      { id: 3, name: '运动' },
      { id: 4, name: '营养' },
      { id: 5, name: '心理' },
      { id: 6, name: '美容' }
    ],
    
    // 内容列表数据
    contentList: [
      {
        id: 1,
        title: '10分钟瑜伽晨练指南',
        description: '每天坚持10分钟瑜伽，让你的身体更加柔韧健康，精神状态也会大大改善',
        image: '/images/wellness.png',
        author: {
          name: '瑜伽老师小美',
          avatar: '/images/wellness.png'
        },
        views: '2.3k',
        likes: '186',
        comments: '45',
        category: 3
      },
      {
        id: 2,
        title: '科学饮水指南：不只是8杯水',
        description: '关于喝水的学问远比你想象的复杂，让我们来了解正确的饮水方式',
        image: '/images/home.png',
        author: {
          name: '营养师王医生',
          avatar: '/images/home.png'
        },
        views: '1.8k',
        likes: '142',
        comments: '38',
        category: 4
      },
      {
        id: 3,
        title: '职场压力管理技巧',
        description: '现代职场压力大，学会这些技巧让你在工作中保持心理健康',
        image: '/images/export.png',
        author: {
          name: '心理咨询师李老师',
          avatar: '/images/export.png'
        },
        views: '3.1k',
        likes: '267',
        comments: '72',
        category: 5
      },
      {
        id: 4,
        title: '天然护肤品制作教程',
        description: '使用天然材料制作护肤品，温和有效，让肌肤自然焕发光彩',
        image: '/images/wellness.png',
        author: {
          name: '美容达人小丽',
          avatar: '/images/wellness.png'
        },
        views: '1.5k',
        likes: '98',
        comments: '23',
        category: 6
      },
      {
        id: 5,
        title: '健康作息习惯养成',
        description: '规律的作息是健康的基础，让我们一起养成良好的生活习惯',
        image: '/images/home.png',
        author: {
          name: '健康管理师张老师',
          avatar: '/images/home.png'
        },
        views: '2.7k',
        likes: '203',
        comments: '56',
        category: 2
      }
    ],
    
    // 热门标签数据
    hotTags: [
      { id: 1, name: '减肥' },
      { id: 2, name: '瑜伽' },
      { id: 3, name: '健身' },
      { id: 4, name: '营养' },
      { id: 5, name: '护肤' },
      { id: 6, name: '心理健康' },
      { id: 7, name: '睡眠' },
      { id: 8, name: '美食' }
    ],
    
    // 推荐用户数据
    recommendUsers: [
      {
        id: 1,
        name: '健身教练小王',
        description: '专业健身指导，5年教学经验',
        avatar: '/images/home.png',
        isFollowed: false
      },
      {
        id: 2,
        name: '营养师小李',
        description: '注册营养师，科学饮食倡导者',
        avatar: '/images/wellness.png',
        isFollowed: true
      },
      {
        id: 3,
        name: '瑜伽导师艾米',
        description: '国际瑜伽认证导师',
        avatar: '/images/export.png',
        isFollowed: false
      },
      {
        id: 4,
        name: '心理咨询师陈老师',
        description: '专业心理咨询，关注心理健康',
        avatar: '/images/home.png',
        isFollowed: false
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 页面加载时执行
    this.loadData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    // 页面初次渲染完成
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 页面显示时执行
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    // 页面隐藏时执行
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 页面卸载时执行
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    // 上拉加载更多
    this.loadMoreContent();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '探索 - 发现更多精彩内容',
      path: '/pages/explore/index'
    };
  },

  /**
   * 加载数据
   */
  loadData: function() {
    wx.showLoading({
      title: '加载中...'
    });
    
    // 模拟网络请求
    setTimeout(() => {
      wx.hideLoading();
      console.log('探索页面数据加载完成');
    }, 800);
  },

  /**
   * 加载更多内容
   */
  loadMoreContent: function() {
    wx.showLoading({
      title: '加载更多...'
    });

    setTimeout(() => {
      const newContent = [
        {
          id: this.data.contentList.length + 1,
          title: '新增健康内容',
          description: '这是一个新加载的健康内容示例',
          image: '/images/wellness.png',
          author: {
            name: '新作者',
            avatar: '/images/wellness.png'
          },
          views: '500',
          likes: '32',
          comments: '8',
          category: 1
        }
      ];

      this.setData({
        contentList: [...this.data.contentList, ...newContent]
      });

      wx.hideLoading();
    }, 1000);
  },

  /**
   * 搜索点击
   */
  onSearchTap: function() {
    wx.showToast({
      title: '搜索功能',
      icon: 'none'
    });
  },

  /**
   * 筛选点击
   */
  onFilterTap: function() {
    wx.showActionSheet({
      itemList: ['按时间排序', '按热度排序', '按点赞数排序'],
      success: (res) => {
        wx.showToast({
          title: `选择了${res.tapIndex + 1}`,
          icon: 'none'
        });
      }
    });
  },

  /**
   * 分类切换
   */
  onCategoryTap: function(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.setData({
      currentCategory: categoryId
    });
    
    // 这里可以根据分类筛选内容
    wx.showToast({
      title: `切换到分类${categoryId}`,
      icon: 'none'
    });
  },

  /**
   * 内容点击
   */
  onContentTap: function(e) {
    const contentId = e.currentTarget.dataset.id;
    wx.showToast({
      title: `查看内容${contentId}`,
      icon: 'none'
    });
    // 可以跳转到内容详情页
  },

  /**
   * 标签点击
   */
  onTagTap: function(e) {
    const tag = e.currentTarget.dataset.tag;
    wx.showToast({
      title: `搜索标签: ${tag}`,
      icon: 'none'
    });
  },

  /**
   * 用户点击
   */
  onUserTap: function(e) {
    const userId = e.currentTarget.dataset.id;
    wx.showToast({
      title: `查看用户${userId}`,
      icon: 'none'
    });
  },

  /**
   * 关注/取消关注
   */
  onFollowTap: function(e) {
    const userId = e.currentTarget.dataset.id;
    const userIndex = this.data.recommendUsers.findIndex(user => user.id === userId);
    
    if (userIndex !== -1) {
      const updatedUsers = [...this.data.recommendUsers];
      updatedUsers[userIndex].isFollowed = !updatedUsers[userIndex].isFollowed;
      
      this.setData({
        recommendUsers: updatedUsers
      });
      
      wx.showToast({
        title: updatedUsers[userIndex].isFollowed ? '关注成功' : '取消关注',
        icon: 'success'
      });
    }
  }
})
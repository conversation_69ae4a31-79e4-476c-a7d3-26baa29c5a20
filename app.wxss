/**app.wxss**/
/* 全局样式 */

/* 重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 通用容器 */
.container {
  min-height: 100vh;
}

/* 通用按钮样式 */
.btn {
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
  outline: none;
}

.btn-primary {
  background: linear-gradient(135deg, #ff9a56, #ffcc70);
  color: #fff;
}

.btn-primary:active {
  opacity: 0.8;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #666;
}

.btn-secondary:active {
  background-color: #e0e0e0;
}

/* 通用文本样式 */
.text-primary {
  color: #333;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 通用布局 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

/* 通用间距 */
.m-10 { margin: 10rpx; }
.m-20 { margin: 20rpx; }
.m-30 { margin: 30rpx; }

.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }

.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }

.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.ml-30 { margin-left: 30rpx; }

.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }
.mr-30 { margin-right: 30rpx; }

.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }

.pt-10 { padding-top: 10rpx; }
.pt-20 { padding-top: 20rpx; }
.pt-30 { padding-top: 30rpx; }

.pb-10 { padding-bottom: 10rpx; }
.pb-20 { padding-bottom: 20rpx; }
.pb-30 { padding-bottom: 30rpx; }

.pl-10 { padding-left: 10rpx; }
.pl-20 { padding-left: 20rpx; }
.pl-30 { padding-left: 30rpx; }

.pr-10 { padding-right: 10rpx; }
.pr-20 { padding-right: 20rpx; }
.pr-30 { padding-right: 30rpx; }

/* 通用卡片样式 */
.card {
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin: 20rpx;
  overflow: hidden;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

/* 错误状态 */
.error {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #ff6b6b;
} 
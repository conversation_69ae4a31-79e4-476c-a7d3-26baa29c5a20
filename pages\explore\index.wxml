<!--pages/explore/index.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input" bindtap="onSearchTap">
      <text class="search-placeholder">🔍 搜索感兴趣的话题...</text>
    </view>
    <view class="filter-btn" bindtap="onFilterTap">筛选</view>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs">
    <view class="tab-item {{currentCategory === item.id ? 'active' : ''}}" 
          wx:for="{{categories}}" 
          wx:key="id" 
          bindtap="onCategoryTap" 
          data-id="{{item.id}}">
      {{item.name}}
    </view>
  </view>

  <!-- 推荐内容 -->
  <view class="content-list">
    <view class="content-item" wx:for="{{contentList}}" wx:key="id" bindtap="onContentTap" data-id="{{item.id}}">
      <image class="content-image" src="{{item.image}}" mode="aspectFill"></image>
      <view class="content-info">
        <view class="content-title">{{item.title}}</view>
        <view class="content-desc">{{item.description}}</view>
        <view class="content-meta">
          <view class="author-info">
            <image class="author-avatar" src="{{item.author.avatar}}" mode="aspectFill"></image>
            <text class="author-name">{{item.author.name}}</text>
          </view>
          <view class="content-stats">
            <text class="stat-item">👁️ {{item.views}}</text>
            <text class="stat-item">❤️ {{item.likes}}</text>
            <text class="stat-item">💬 {{item.comments}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 热门标签 -->
  <view class="hot-tags-section">
    <view class="section-title">热门标签</view>
    <view class="tags-container">
      <view class="tag-item" wx:for="{{hotTags}}" wx:key="id" bindtap="onTagTap" data-tag="{{item.name}}">
        {{item.name}}
      </view>
    </view>
  </view>

  <!-- 推荐用户 -->
  <view class="recommend-users-section">
    <view class="section-title">推荐关注</view>
    <view class="users-list">
      <view class="user-item" wx:for="{{recommendUsers}}" wx:key="id" bindtap="onUserTap" data-id="{{item.id}}">
        <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
        <view class="user-info">
          <text class="user-name">{{item.name}}</text>
          <text class="user-desc">{{item.description}}</text>
        </view>
        <view class="follow-btn {{item.isFollowed ? 'followed' : ''}}" 
              bindtap="onFollowTap" 
              data-id="{{item.id}}"
              catchtap="true">
          {{item.isFollowed ? '已关注' : '关注'}}
        </view>
      </view>
    </view>
  </view>
</view>
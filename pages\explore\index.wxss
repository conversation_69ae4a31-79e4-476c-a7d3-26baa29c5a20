/* 探索页面样式 */
page {
  background-color: #f5f5f5;
}

.container {
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #fff;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  margin-right: 20rpx;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

.filter-btn {
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, #ff9a56, #ffcc70);
  color: #fff;
  border-radius: 30rpx;
  font-size: 26rpx;
}

/* 分类标签样式 */
.category-tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  overflow-x: auto;
  white-space: nowrap;
}

.tab-item {
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  background-color: #f5f5f5;
  color: #666;
  flex-shrink: 0;
}

.tab-item.active {
  background: linear-gradient(135deg, #ff9a56, #ffcc70);
  color: #fff;
}

/* 内容列表样式 */
.content-list {
  padding: 0 30rpx;
}

.content-item {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.content-image {
  width: 100%;
  height: 400rpx;
}

.content-info {
  padding: 24rpx;
}

.content-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.content-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  /* 限制显示三行 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.content-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.author-name {
  font-size: 24rpx;
  color: #999;
}

.content-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  font-size: 22rpx;
  color: #999;
}

/* 热门标签区域样式 */
.hot-tags-section {
  margin: 40rpx 30rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.tag-item:active {
  background: linear-gradient(135deg, #ff9a56, #ffcc70);
  color: #fff;
}

/* 推荐用户区域样式 */
.recommend-users-section {
  margin: 40rpx 30rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
}

.users-list {
  margin-top: 20rpx;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-item:last-child {
  border-bottom: none;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 24rpx;
  color: #999;
}

.follow-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #ff9a56, #ffcc70);
  color: #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.follow-btn.followed {
  background-color: #f5f5f5;
  color: #999;
}

.follow-btn:active {
  opacity: 0.8;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .search-input {
    height: 70rpx;
  }
  
  .content-image {
    height: 350rpx;
  }
  
  .content-title {
    font-size: 30rpx;
  }
  
  .content-desc {
    font-size: 24rpx;
  }
  
  .user-avatar {
    width: 70rpx;
    height: 70rpx;
  }
}

@media screen and (min-width: 768px) {
  .container {
    max-width: 750rpx;
    margin: 0 auto;
  }
} 
# 话题热榜小程序

基于微信小程序技术栈开发的话题热榜应用，还原移动端原生应用的UI设计和交互体验。

## 功能特点

- 📱 响应式设计，完美适配移动端
- 🎨 现代化UI设计，采用卡片式布局
- 🔄 支持下拉刷新和上拉加载更多
- 📖 话题列表展示，包含参与人数和讨论功能
- 🧭 底部导航栏切换不同页面
- ✨ 流畅的动画和交互效果

## 技术栈

- **框架**: 微信小程序原生开发
- **样式**: WXSS (支持Flexbox布局)
- **脚本**: JavaScript ES6+
- **构建工具**: 微信开发者工具

## 项目结构

```
topic-top/
├── pages/                 # 页面文件
│   ├── index/             # 话题热榜主页
│   │   ├── index.wxml     # 页面结构
│   │   ├── index.wxss     # 页面样式
│   │   ├── index.js       # 页面逻辑
│   │   └── index.json     # 页面配置
│   ├── home/              # 首页（占位）
│   └── explore/           # 探索页（占位）
├── app.js                 # 小程序入口文件
├── app.json               # 全局配置文件
├── app.wxss               # 全局样式文件
├── sitemap.json           # 站点地图配置
├── project.config.json    # 项目配置文件
└── README.md              # 项目说明文档
```

## 页面说明

### 话题热榜页面 (pages/index)

主要功能：
- 显示话题列表
- 每个话题包含图标、标题、参与人数
- 支持点击话题查看详情
- 支持点击"加入讨论"按钮
- 支持下拉刷新和上拉加载更多

### 响应式设计

项目采用响应式设计，支持不同屏幕尺寸：

- **小屏幕 (≤375px)**: 缩小字体和间距，优化触摸体验
- **大屏幕 (≥768px)**: 限制最大宽度，居中显示

## 开发指南

### 环境要求

- 微信开发者工具 (最新版本)
- Node.js 22.16.0 (推荐使用 fnm 管理版本)

### 安装和运行

1. 下载项目代码
2. 使用微信开发者工具打开项目文件夹
3. 配置小程序AppID (在project.config.json中修改)
4. 点击"编译"按钮运行项目

### 开发说明

- 所有尺寸单位使用 `rpx` (响应式像素)
- 代码注释全部使用中文
- 遵循微信小程序开发规范
- 支持下拉刷新和上拉加载更多功能

## 主要文件说明

- `pages/index/index.wxml`: 页面结构，使用语义化标签
- `pages/index/index.wxss`: 页面样式，包含响应式设计
- `pages/index/index.js`: 页面逻辑，包含数据管理和事件处理
- `app.wxss`: 全局样式，定义通用样式类

## 自定义配置

可以在以下文件中进行自定义配置：

- `app.json`: 修改全局配置，如导航栏颜色、标题等
- `pages/index/index.json`: 修改页面特定配置
- `project.config.json`: 修改项目配置，如AppID等

## 扩展功能

项目预留了扩展接口，可以轻松添加：

- 真实API接口对接
- 用户登录和授权
- 话题详情页面
- 评论和点赞功能
- 搜索功能
- 个人中心页面

## 注意事项

1. 请确保在微信开发者工具中配置正确的AppID
2. 部分功能需要在真机上测试（如分享功能）
3. 正式发布前需要配置合法域名和业务域名
4. 建议使用真实数据替换模拟数据

## 兼容性

- 支持微信小程序基础库 2.19.4+
- 兼容iOS和Android系统
- 支持各种屏幕尺寸的移动设备 
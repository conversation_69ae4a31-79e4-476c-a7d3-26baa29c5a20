// pages/home/<USER>
Page({

  /**
   * 页面的初始数据
   */
  data: {
    currentTime: '',
    // 轮播图数据
    bannerList: [
      {
        id: 1,
        title: '健康生活从今天开始',
        image: '/images/home.png'
      },
      {
        id: 2,
        title: '养生知识大全分享',
        image: '/images/wellness.png'
      },
      {
        id: 3,
        title: '运动健身指导',
        image: '/images/export.png'
      }
    ],
    
    // 快捷菜单数据
    quickMenus: [
      {
        id: 1,
        name: '健康测试',
        icon: '🏥',
        iconClass: 'icon-health',
        type: 'health'
      },
      {
        id: 2,
        name: '营养搭配',
        icon: '🥗',
        iconClass: 'icon-nutrition',
        type: 'nutrition'
      },
      {
        id: 3,
        name: '运动计划',
        icon: '🏃',
        iconClass: 'icon-sport',
        type: 'sport'
      },
      {
        id: 4,
        name: '睡眠监测',
        icon: '😴',
        iconClass: 'icon-sleep',
        type: 'sleep'
      }
    ],
    
    // 热门推荐数据
    recommendList: [
      {
        id: 1,
        title: '春季养生指南',
        description: '春季是养生的最佳时节，了解这些知识让你更健康',
        image: '/images/wellness.png',
        views: 1200,
        likes: 89
      },
      {
        id: 2,
        title: '健康饮食搭配',
        description: '科学的饮食搭配是健康的基础，学会这些技巧',
        image: '/images/home.png',
        views: 856,
        likes: 67
      },
      {
        id: 3,
        title: '居家运动指南',
        description: '在家也能保持运动，简单有效的居家健身方法',
        image: '/images/export.png',
        views: 743,
        likes: 52
      }
    ],
    
    // 最新动态数据
    newsList: [
      {
        id: 1,
        title: '世界卫生组织发布最新健康指南',
        time: '2小时前',
        image: '/images/wellness.png'
      },
      {
        id: 2,
        title: '研究表明：每天8杯水真的有必要吗？',
        time: '4小时前',
        image: '/images/home.png'
      },
      {
        id: 3,
        title: '新型健康食品上市，营养价值翻倍',
        time: '6小时前',
        image: '/images/export.png'
      },
      {
        id: 4,
        title: '专家解答：如何保持良好的作息习惯',
        time: '8小时前',
        image: '/images/wellness.png'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 页面加载时执行
    this.loadData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    // 页面初次渲染完成
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 页面显示时执行
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    // 页面隐藏时执行
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 页面卸载时执行
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    // 上拉加载更多
    this.loadMoreNews();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '健康生活 - 首页',
      path: '/pages/home/<USER>'
    };
  },

  /**
   * 加载数据
   */
  loadData: function() {
    // 设置当前时间
    const now = new Date();
    const timeString = now.getFullYear() + '-' +
                      (now.getMonth() + 1).toString().padStart(2, '0') + '-' +
                      now.getDate().toString().padStart(2, '0') + ' ' +
                      now.getHours().toString().padStart(2, '0') + ':' +
                      now.getMinutes().toString().padStart(2, '0');

    this.setData({
      currentTime: timeString
    });

    wx.showLoading({
      title: '加载中...'
    });

    // 模拟网络请求
    setTimeout(() => {
      wx.hideLoading();
      console.log('首页数据加载完成');
    }, 800);
  },

  /**
   * 加载更多新闻
   */
  loadMoreNews: function() {
    wx.showLoading({
      title: '加载更多...'
    });

    setTimeout(() => {
      const newNews = [
        {
          id: this.data.newsList.length + 1,
          title: '最新健康资讯：维生素D的重要性',
          time: '10小时前',
          image: '/images/home.png'
        }
      ];

      this.setData({
        newsList: [...this.data.newsList, ...newNews]
      });

      wx.hideLoading();
    }, 1000);
  },

  /**
   * 轮播图点击
   */
  onBannerTap: function(e) {
    const bannerId = e.currentTarget.dataset.id;
    wx.showToast({
      title: `点击轮播图${bannerId}`,
      icon: 'none'
    });
  },

  /**
   * 快捷菜单点击
   */
  onQuickMenuTap: function(e) {
    const type = e.currentTarget.dataset.type;
    wx.showToast({
      title: `进入${type}功能`,
      icon: 'none'
    });
  },

  /**
   * 推荐内容点击
   */
  onRecommendTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.showToast({
      title: `查看推荐${id}`,
      icon: 'none'
    });
  },

  /**
   * 新闻点击
   */
  onNewsTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.showToast({
      title: `查看新闻${id}`,
      icon: 'none'
    });
  },

  /**
   * 更多推荐
   */
  onMoreRecommendTap: function() {
    wx.showToast({
      title: '查看更多推荐',
      icon: 'none'
    });
  },

  /**
   * 更多新闻
   */
  onMoreNewsTap: function() {
    wx.showToast({
      title: '查看更多新闻',
      icon: 'none'
    });
  }
})
// 话题热榜页面逻辑
Page({
  /**
   * 页面的初始数据
   */
  data: {
    topicList: [
      {
        id: 1,
        title: '话题热榜找出你最关心的事实上事...',
        participantCount: 112
      },
      {
        id: 2,
        title: '话题热榜',
        participantCount: 112
      },
      {
        id: 3,
        title: '话题热榜',
        participantCount: 112
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function () {
    this.loadTopicData();
  }
}); 

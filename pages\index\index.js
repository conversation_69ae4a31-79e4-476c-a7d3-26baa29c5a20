// 话题热榜页面逻辑
Page({
  /**
   * 页面的初始数据
   */
  data: {
    topicList: [
      {
        id: 1,
        title: '话题热榜找出你最关心的事实上事...',
        participantCount: 112
      },
      {
        id: 2,
        title: '话题热榜',
        participantCount: 112
      },
      {
        id: 3,
        title: '话题热榜',
        participantCount: 112
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function () {
    this.loadTopicData();
  },

  /**
   * 加载话题数据
   */
  loadTopicData: function () {
    // 这里可以从服务器获取数据，目前使用静态数据
    console.log('加载话题数据');
    // 数据已经在data中定义，无需额外处理
  },

  /**
   * 点击话题项
   */
  onTopicTap: function (e) {
    const topicId = e.currentTarget.dataset.id;
    console.log('点击话题:', topicId);
    // 这里可以跳转到话题详情页
    wx.showToast({
      title: '点击了话题',
      icon: 'success'
    });
  },

  /**
   * 点击加入讨论
   */
  onJoinTap: function (e) {
    const topicId = e.currentTarget.dataset.id;
    console.log('加入讨论:', topicId);
    wx.showToast({
      title: '加入讨论',
      icon: 'success'
    });
  },

  /**
   * 点击更多话题
   */
  onMoreTap: function () {
    console.log('查看更多话题');
    wx.showToast({
      title: '更多话题',
      icon: 'success'
    });
  }
});

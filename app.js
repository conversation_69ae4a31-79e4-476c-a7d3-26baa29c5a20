// 小程序入口文件
App({
  /**
   * 当小程序初始化完成时，会触发 onLaunch（全局只触发一次）
   */
  onLaunch: function () {
    console.log('小程序启动完成');
    
    // 获取用户信息
    this.getUserInfo();
    
    // 检查更新
    this.checkUpdate();
  },

  /**
   * 当小程序启动，或从后台进入前台显示，会触发 onShow
   */
  onShow: function (options) {
    console.log('小程序显示');
  },

  /**
   * 当小程序从前台进入后台，会触发 onHide
   */
  onHide: function () {
    console.log('小程序隐藏');
  },

  /**
   * 当小程序发生脚本错误，或者 api 调用失败时，会触发 onError 并带上错误信息
   */
  onError: function (msg) {
    console.error('小程序错误:', msg);
  },

  /**
   * 获取用户信息
   */
  getUserInfo: function () {
    // 获取用户设置
    wx.getSetting({
      success: res => {
        console.log('用户授权设置:', res.authSetting);
        // 注意：wx.getUserInfo 已废弃，这里仅作为示例
        // 实际项目中应使用 wx.getUserProfile 或其他方式
      }
    });
  },

  /**
   * 检查小程序更新
   */
  checkUpdate: function () {
    const updateManager = wx.getUpdateManager();

    updateManager.onCheckForUpdate(function (res) {
      // 请求完新版本信息的回调
      console.log('是否有新版本:', res.hasUpdate);
    });

    updateManager.onUpdateReady(function () {
      wx.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success: function (res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate();
          }
        }
      });
    });

    updateManager.onUpdateFailed(function () {
      // 新版本下载失败
      console.log('新版本下载失败');
    });
  },

  /**
   * 全局数据
   */
  globalData: {
    userInfo: null,
    version: '1.0.0'
  }
}); 